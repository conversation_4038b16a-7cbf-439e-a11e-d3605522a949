//@version=5
indicator("Fixed Range Volume Profile", overlay=true, max_boxes_count=500, max_lines_count=500, max_labels_count=500)

// === INPUTS ===
// General Settings
group_general = "General Settings"
show_profile = input.bool(true, "Show Volume Profile", group=group_general)
show_poc = input.bool(true, "Show POC Line", group=group_general)
show_vah = input.bool(true, "Show VAH Line", group=group_general)
show_val = input.bool(true, "Show VAL Line", group=group_general)
show_range_box = input.bool(true, "Show Range Box", group=group_general)
max_periods = input.int(5, "Max Periods to Display", minval=1, maxval=20, tooltip="Number of profiles to keep on chart", group=group_general)

// Volume Profile Settings
group_vp = "Volume Profile Settings"
profile_rows = input.int(24, "Profile Rows", minval=10, maxval=100, tooltip="Number of price levels in profile", group=group_vp)
value_area_percent = input.float(70.0, "Value Area %", minval=50.0, maxval=95.0, step=5.0, tooltip="Percentage for Value Area calculation", group=group_vp)
profile_width = input.float(50.0, "Profile Width", minval=10.0, maxval=200.0, step=10.0, tooltip="Width of volume profile bars", group=group_vp)

// Style Settings
group_style = "Style Settings"
poc_color = input.color(color.white, "POC Color", group=group_style)
vah_color = input.color(color.red, "VAH Color", group=group_style)
val_color = input.color(color.green, "VAL Color", group=group_style)
profile_color = input.color(color.new(color.blue, 80), "Profile Color", group=group_style)
range_box_color = input.color(color.new(color.yellow, 90), "Range Box Color", group=group_style)
line_width = input.int(2, "Line Width", minval=1, maxval=5, group=group_style)

// Time Settings
group_time = "Time Settings"
day_start_hour = input.int(0, "Day Start Hour (0-23)", minval=0, maxval=23, tooltip="Hour when new day starts", group=group_time)

// === VARIABLES ===
// Arrays to store profile data
var profile_boxes = array.new<box>()
var poc_lines = array.new<line>()
var vah_lines = array.new<line>()
var val_lines = array.new<line>()
var range_boxes = array.new<box>()

// Current session tracking
var float session_high = na
var float session_low = na
var int session_start_bar = na
var bool high_first = na
var bool range_determined = false
var float range_high = na
var float range_low = na
var int range_start_bar = na

// Volume profile data
var volume_data = array.new<float>()
var price_levels = array.new<float>()

// === HELPER FUNCTIONS ===
// Function to manage array size
manage_array_size(arr, max_size) =>
    while array.size(arr) > max_size
        old_item = array.pop(arr)
        if not na(old_item)
            box.delete(old_item)

manage_line_array_size(arr, max_size) =>
    while array.size(arr) > max_size
        old_item = array.pop(arr)
        if not na(old_item)
            line.delete(old_item)

// Calculate volume profile
calculate_volume_profile(start_bar, end_bar, high_price, low_price) =>
    if na(start_bar) or na(end_bar) or na(high_price) or na(low_price)
        [na, na, na, na]
    else
        // Clear previous data
        array.clear(volume_data)
        array.clear(price_levels)
        
        // Calculate price step
        price_range = high_price - low_price
        price_step = price_range / profile_rows
        
        // Initialize arrays
        for i = 0 to profile_rows - 1
            array.push(volume_data, 0.0)
            array.push(price_levels, low_price + i * price_step)
        
        // Accumulate volume for each price level
        total_volume = 0.0
        for bar_idx = start_bar to end_bar
            if bar_idx <= bar_index
                bar_high = high[bar_index - bar_idx]
                bar_low = low[bar_index - bar_idx]
                bar_volume = volume[bar_index - bar_idx]
                
                if not na(bar_volume) and bar_volume > 0
                    total_volume := total_volume + bar_volume
                    
                    // Distribute volume across price levels that the bar touched
                    for i = 0 to profile_rows - 1
                        level_price = array.get(price_levels, i)
                        level_high = level_price + price_step
                        
                        // Check if this price level intersects with the bar
                        if level_price <= bar_high and level_high >= bar_low
                            overlap_high = math.min(bar_high, level_high)
                            overlap_low = math.max(bar_low, level_price)
                            overlap_ratio = (overlap_high - overlap_low) / (bar_high - bar_low)
                            
                            if overlap_ratio > 0
                                current_vol = array.get(volume_data, i)
                                array.set(volume_data, i, current_vol + bar_volume * overlap_ratio)
        
        // Find POC (Point of Control)
        max_volume = 0.0
        poc_index = 0
        for i = 0 to array.size(volume_data) - 1
            vol = array.get(volume_data, i)
            if vol > max_volume
                max_volume := vol
                poc_index := i
        
        poc_price = array.get(price_levels, poc_index)
        
        // Calculate Value Area (VAH and VAL)
        target_volume = total_volume * value_area_percent / 100.0
        accumulated_volume = max_volume
        
        upper_index = poc_index
        lower_index = poc_index
        
        // Expand from POC until we reach target volume
        while accumulated_volume < target_volume and (upper_index < array.size(volume_data) - 1 or lower_index > 0)
            upper_vol = upper_index < array.size(volume_data) - 1 ? array.get(volume_data, upper_index + 1) : 0.0
            lower_vol = lower_index > 0 ? array.get(volume_data, lower_index - 1) : 0.0
            
            if upper_vol >= lower_vol and upper_index < array.size(volume_data) - 1
                upper_index := upper_index + 1
                accumulated_volume := accumulated_volume + upper_vol
            else if lower_index > 0
                lower_index := lower_index - 1
                accumulated_volume := accumulated_volume + lower_vol
            else
                break
        
        vah_price = array.get(price_levels, upper_index) + price_step
        val_price = array.get(price_levels, lower_index)
        
        [poc_price, vah_price, val_price, max_volume]

// Draw volume profile bars
draw_profile_bars(start_bar, end_bar, high_price, low_price, max_vol) =>
    if show_profile and not na(start_bar) and not na(end_bar) and array.size(volume_data) > 0
        price_step = (high_price - low_price) / profile_rows
        
        for i = 0 to array.size(volume_data) - 1
            vol = array.get(volume_data, i)
            if vol > 0
                level_price = array.get(price_levels, i)
                level_high = level_price + price_step
                
                // Calculate bar width based on volume
                bar_width = (vol / max_vol) * profile_width
                bar_right = end_bar + int(bar_width)
                
                // Create volume bar
                profile_box = box.new(
                    left=end_bar, top=level_high,
                    right=bar_right, bottom=level_price,
                    bgcolor=profile_color,
                    border_color=color.new(profile_color, 50),
                    border_width=1
                )
                array.unshift(profile_boxes, profile_box)

// === CORE LOGIC ===
// Detect new day
is_new_day = timeframe.change("1D")

// Reset session tracking on new day
if is_new_day or barstate.isfirst
    session_high := high
    session_low := low
    session_start_bar := bar_index
    high_first := na
    range_determined := false
    range_high := na
    range_low := na
    range_start_bar := na
else
    // Update session high and low
    if high > session_high
        session_high := high
        if na(high_first)
            high_first := true
    
    if low < session_low
        session_low := low
        if na(high_first)
            high_first := false

// Determine range when first extreme is hit
if not range_determined and not na(high_first)
    if high_first
        // High came first, range is from session high to session low
        range_high := session_high
        range_low := session_low
        range_start_bar := session_start_bar
        range_determined := true
    else
        // Low came first, range is from session low to session high
        range_high := session_high
        range_low := session_low
        range_start_bar := session_start_bar
        range_determined := true

// === DRAWING LOGIC ===
// Draw range box and volume profile when range is determined
if range_determined and not na(range_high) and not na(range_low) and not na(range_start_bar)
    // Calculate volume profile
    [poc, vah, val, max_vol] = calculate_volume_profile(range_start_bar, bar_index, range_high, range_low)
    
    // Draw range box
    if show_range_box
        range_box = box.new(
            left=range_start_bar, top=range_high,
            right=bar_index, bottom=range_low,
            bgcolor=range_box_color,
            border_color=color.new(range_box_color, 30),
            border_width=1
        )
        array.unshift(range_boxes, range_box)
    
    // Draw volume profile bars
    if not na(max_vol) and max_vol > 0
        draw_profile_bars(range_start_bar, bar_index, range_high, range_low, max_vol)
    
    // Draw POC line
    if show_poc and not na(poc)
        poc_line = line.new(
            x1=range_start_bar, y1=poc,
            x2=bar_index, y2=poc,
            color=poc_color,
            width=line_width,
            style=line.style_solid,
            extend=extend.right
        )
        array.unshift(poc_lines, poc_line)
    
    // Draw VAH line
    if show_vah and not na(vah)
        vah_line = line.new(
            x1=range_start_bar, y1=vah,
            x2=bar_index, y2=vah,
            color=vah_color,
            width=line_width,
            style=line.style_solid,
            extend=extend.right
        )
        array.unshift(vah_lines, vah_line)
    
    // Draw VAL line
    if show_val and not na(val)
        val_line = line.new(
            x1=range_start_bar, y1=val,
            x2=bar_index, y2=val,
            color=val_color,
            width=line_width,
            style=line.style_solid,
            extend=extend.right
        )
        array.unshift(val_lines, val_line)

// Manage array sizes to prevent memory issues
manage_array_size(profile_boxes, max_periods * profile_rows)
manage_array_size(range_boxes, max_periods)
manage_line_array_size(poc_lines, max_periods)
manage_line_array_size(vah_lines, max_periods)
manage_line_array_size(val_lines, max_periods)
