//@version=5
indicator("Daily High/Low & Day Open Lines", overlay=true, max_lines_count=500)

// === INPUTS ===
// General Settings
group_general = "General Settings"
show_daily_high = input.bool(true, "Show Daily High Line", group=group_general)
show_daily_low = input.bool(true, "Show Daily Low Line", group=group_general)
show_day_open_line = input.bool(true, "Show Day Open Vertical Line", group=group_general)
max_periods = input.int(10, "Max Periods to Display", minval=1, maxval=50, tooltip="Number of days to keep on chart", group=group_general)

// Time Settings
group_time = "Time Settings"
day_start_hour = input.int(0, "Day Start Hour (0-23)", minval=0, maxval=23, tooltip="Hour when new day starts (ICT: 0 UTC)", group=group_time)

// Style Settings
group_style = "Style Settings"
high_color = input.color(color.white, "Daily High Color", group=group_style)
low_color = input.color(color.white, "Daily Low Color", group=group_style)
day_open_color = input.color(color.gray, "Day Open Line Color", group=group_style)
line_width = input.int(1, "Line Width", minval=1, maxval=3, group=group_style)
extend_lines = input.string("Right", "Extend Lines", options=["None", "Right", "Left", "Both"], group=group_style)

// === HELPER FUNCTIONS ===
get_extend_type(extend_type) =>
    switch extend_type
        "None" => extend.none
        "Right" => extend.right
        "Left" => extend.left
        "Both" => extend.both
        => extend.right

// === VARIABLES ===
var extend_setting = get_extend_type(extend_lines)

// Arrays to store lines
var daily_high_lines = array.new_line()
var daily_low_lines = array.new_line()
var day_open_lines = array.new_line()

// === CORE LOGIC ===
// Detect new day
is_new_day = timeframe.change("1D")

// Get current day's high and low
day_high = request.security(syminfo.tickerid, "1D", high, lookahead=barmerge.lookahead_off)
day_low = request.security(syminfo.tickerid, "1D", low, lookahead=barmerge.lookahead_off)

// Get previous day's high and low
prev_day_high = request.security(syminfo.tickerid, "1D", high[1], lookahead=barmerge.lookahead_off)
prev_day_low = request.security(syminfo.tickerid, "1D", low[1], lookahead=barmerge.lookahead_off)

// Function to manage array size
manage_array_size(arr, max_size) =>
    while array.size(arr) > max_size
        old_item = array.pop(arr)
        if not na(old_item)
            line.delete(old_item)

// === DRAWING LOGIC ===
// Draw vertical line at day open
if is_new_day and show_day_open_line
    day_open_line = line.new(x1=bar_index, y1=low,x2=bar_index, y2=high,color=day_open_color,width=line_width,style=line.style_dashed,extend=extend.both)
    array.unshift(day_open_lines, day_open_line)
    manage_array_size(day_open_lines, max_periods)

// Draw horizontal lines for daily high and low
if show_daily_high and not na(day_high)
    // Remove old high lines
    if array.size(daily_high_lines) > 0
        for i = array.size(daily_high_lines) - 1 to 0
            old_line = array.get(daily_high_lines, i)
            if not na(old_line)
                line.delete(old_line)
        array.clear(daily_high_lines)

    // Draw new high line
    high_line = line.new(x1=bar_index - 200, y1=day_high,x2=bar_index, y2=day_high,color=high_color,width=line_width,style=line.style_solid,extend=extend_setting)
    array.push(daily_high_lines, high_line)

if show_daily_low and not na(day_low)
    // Remove old low lines
    if array.size(daily_low_lines) > 0
        for i = array.size(daily_low_lines) - 1 to 0
            old_line = array.get(daily_low_lines, i)
            if not na(old_line)
                line.delete(old_line)
        array.clear(daily_low_lines)

    // Draw new low line
    low_line = line.new(x1=bar_index - 200, y1=day_low,x2=bar_index, y2=day_low,color=low_color,width=line_width,style=line.style_solid,extend=extend_setting)
    array.push(daily_low_lines, low_line)
