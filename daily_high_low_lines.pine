//@version=5
indicator("Daily High/Low & Day Open Lines", overlay=true, max_lines_count=500)

// === INPUTS ===
// General Settings
group_general = "General Settings"
show_daily_high = input.bool(true, "Show Daily High Line", group=group_general)
show_daily_low = input.bool(true, "Show Daily Low Line", group=group_general)
show_day_open_line = input.bool(true, "Show Day Open Vertical Line", group=group_general)
max_periods = input.int(10, "Max Periods to Display", minval=1, maxval=50, tooltip="Number of days to keep on chart", group=group_general)

// Time Settings
group_time = "Time Settings"
day_start_hour = input.int(0, "Day Start Hour (0-23)", minval=0, maxval=23, tooltip="Hour when new day starts (ICT: 0 UTC)", group=group_time)

// Style Settings
group_style = "Style Settings"
high_color = input.color(color.white, "Daily High Color", group=group_style)
low_color = input.color(color.white, "Daily Low Color", group=group_style)
day_open_color = input.color(color.gray, "Day Open Line Color", group=group_style)
line_width = input.int(1, "Line Width", minval=1, maxval=3, group=group_style)
extend_lines = input.string("Right", "Extend Lines", options=["None", "Right", "Left", "Both"], group=group_style)

// === HELPER FUNCTIONS ===
get_extend_type(extend_type) =>
    switch extend_type
        "None" => extend.none
        "Right" => extend.right
        "Left" => extend.left
        "Both" => extend.both
        => extend.right

// === VARIABLES ===
var extend_setting = get_extend_type(extend_lines)

// Arrays to store lines
var daily_high_lines = array.new_line()
var daily_low_lines = array.new_line()
var day_open_lines = array.new_line()

// === CORE LOGIC ===
// Detect new day
is_new_day = timeframe.change("1D")

// Variables to store daily levels
var float current_day_high = na
var float current_day_low = na
var int day_start_bar = na

// Update daily levels
if is_new_day or barstate.isfirst
    // Store the previous day's levels before updating
    if not na(current_day_high) and not na(current_day_low) and not na(day_start_bar)
        // Create historical lines for the completed day
        if show_daily_high
            historical_high_line = line.new(
                x1=day_start_bar, y1=current_day_high,
                x2=bar_index - 1, y2=current_day_high,
                color=high_color,
                width=line_width,
                style=line.style_solid,
                extend=extend.none
            )
            array.unshift(daily_high_lines, historical_high_line)

        if show_daily_low
            historical_low_line = line.new(
                x1=day_start_bar, y1=current_day_low,
                x2=bar_index - 1, y2=current_day_low,
                color=low_color,
                width=line_width,
                style=line.style_solid,
                extend=extend.none
            )
            array.unshift(daily_low_lines, historical_low_line)

    // Start new day tracking
    current_day_high := high
    current_day_low := low
    day_start_bar := bar_index
else
    // Update current day's high and low
    current_day_high := math.max(current_day_high, high)
    current_day_low := math.min(current_day_low, low)

// Function to manage array size
manage_array_size(arr, max_size) =>
    while array.size(arr) > max_size
        old_item = array.pop(arr)
        if not na(old_item)
            line.delete(old_item)

// Variables for current day's extending lines
var line current_high_line = na
var line current_low_line = na

// === DRAWING LOGIC ===
// Draw vertical line at day open
if is_new_day and show_day_open_line
    day_open_line = line.new(
        x1=bar_index, y1=low,
        x2=bar_index, y2=high,
        color=day_open_color,
        width=line_width,
        style=line.style_dashed,
        extend=extend.both
    )
    array.unshift(day_open_lines, day_open_line)
    manage_array_size(day_open_lines, max_periods)

// Manage historical line arrays
manage_array_size(daily_high_lines, max_periods)
manage_array_size(daily_low_lines, max_periods)

// Draw/update current day's extending lines
if show_daily_high and not na(current_day_high) and not na(day_start_bar)
    // Delete previous current line
    if not na(current_high_line)
        line.delete(current_high_line)

    // Create new extending line for current day
    current_high_line := line.new(
        x1=day_start_bar, y1=current_day_high,
        x2=bar_index, y2=current_day_high,
        color=high_color,
        width=line_width,
        style=line.style_solid,
        extend=extend_setting
    )

if show_daily_low and not na(current_day_low) and not na(day_start_bar)
    // Delete previous current line
    if not na(current_low_line)
        line.delete(current_low_line)

    // Create new extending line for current day
    current_low_line := line.new(
        x1=day_start_bar, y1=current_day_low,
        x2=bar_index, y2=current_day_low,
        color=low_color,
        width=line_width,
        style=line.style_solid,
        extend=extend_setting
    )
