//@version=5
indicator("Daily High/Low & Day Open Lines", overlay=true, max_lines_count=500)

// === INPUTS ===
// General Settings
group_general = "General Settings"
show_daily_high = input.bool(true, "Show Daily High Line", group=group_general)
show_daily_low = input.bool(true, "Show Daily Low Line", group=group_general)
show_day_open_line = input.bool(true, "Show Day Open Vertical Line", group=group_general)
max_periods = input.int(5, "Max Periods to Display", minval=1, maxval=20, tooltip="Number of days to keep on chart", group=group_general)

// Time Settings
group_time = "Time Settings"
day_start_hour = input.int(0, "Day Start Hour (0-23)", minval=0, maxval=23, tooltip="Hour when new day starts (ICT: 0 UTC)", group=group_time)
timezone = input.string("UTC", "Time Zone", options=["UTC", "America/New_York", "Europe/London", "Asia/Tokyo"], group=group_time)

// Style Settings
group_style = "Style Settings"
high_color = input.color(color.red, "Daily High Color", group=group_style)
low_color = input.color(color.green, "Daily Low Color", group=group_style)
day_open_color = input.color(color.blue, "Day Open Line Color", group=group_style)
line_width = input.int(2, "Line Width", minval=1, maxval=5, group=group_style)
line_style = input.string("Solid", "Line Style", options=["Solid", "Dashed", "Dotted"], group=group_style)
extend_lines = input.string("Right", "Extend Lines", options=["None", "Right", "Left", "Both"], group=group_style)

// Label Settings
group_labels = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_labels)
show_prices = input.bool(true, "Show Prices in Labels", group=group_labels)
label_size = input.string("Normal", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_labels)

// === HELPER FUNCTIONS ===
get_line_style(style) =>
    switch style
        "Solid" => line.style_solid
        "Dashed" => line.style_dashed
        "Dotted" => line.style_dotted
        => line.style_solid

get_extend_type(extend_type) =>
    switch extend_type
        "None" => extend.none
        "Right" => extend.right
        "Left" => extend.left
        "Both" => extend.both
        => extend.right

get_label_size(size) =>
    switch size
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
        => size.normal

// === VARIABLES ===
var line_style_setting = get_line_style(line_style)
var extend_setting = get_extend_type(extend_lines)
var label_size_setting = get_label_size(label_size)

// Arrays to store lines and labels
var daily_high_lines = array.new_line()
var daily_low_lines = array.new_line()
var day_open_lines = array.new_line()
var daily_high_labels = array.new_label()
var daily_low_labels = array.new_label()

// === CORE LOGIC ===
// Define session for true day open detection
day_hour_str = day_start_hour < 10 ? "0" + str.tostring(day_start_hour) : str.tostring(day_start_hour)
day_next_hour = (day_start_hour + 1) % 24
day_next_hour_str = day_next_hour < 10 ? "0" + str.tostring(day_next_hour) : str.tostring(day_next_hour)
day_session = day_hour_str + "00-" + day_next_hour_str + "00"

// Detect new day based on configured hour and timezone
is_new_day = not na(time("D", day_session, timezone)) and na(time("D", day_session, timezone))[1]

// Get previous day's high and low using security function
[prev_high, prev_low] = request.security(syminfo.tickerid, "1D", [high[1], low[1]], lookahead=barmerge.lookahead_off)

// Function to manage array size
manage_array_size(arr, max_size) =>
    while array.size(arr) > max_size
        old_item = array.pop(arr)
        if not na(old_item)
            line.delete(old_item)

// Function to manage label array size
manage_label_array_size(arr, max_size) =>
    while array.size(arr) > max_size
        old_label = array.pop(arr)
        if not na(old_label)
            label.delete(old_label)

// === DRAWING LOGIC ===
if is_new_day or barstate.isfirst
    // Draw vertical line at day open
    if show_day_open_line
        day_open_line = line.new(
            x1=bar_index, y1=low, 
            x2=bar_index, y2=high, 
            color=day_open_color, 
            width=line_width, 
            style=line.style_dashed, 
            extend=extend.both
        )
        array.unshift(day_open_lines, day_open_line)
        manage_array_size(day_open_lines, max_periods)

// Draw horizontal lines for previous day's high and low
if barstate.islast and not na(prev_high) and not na(prev_low)
    // Clear existing lines and labels
    for i = 0 to array.size(daily_high_lines) - 1
        line.delete(array.get(daily_high_lines, i))
    for i = 0 to array.size(daily_low_lines) - 1
        line.delete(array.get(daily_low_lines, i))
    for i = 0 to array.size(daily_high_labels) - 1
        label.delete(array.get(daily_high_labels, i))
    for i = 0 to array.size(daily_low_labels) - 1
        label.delete(array.get(daily_low_labels, i))
    
    array.clear(daily_high_lines)
    array.clear(daily_low_lines)
    array.clear(daily_high_labels)
    array.clear(daily_low_labels)
    
    // Draw daily high line
    if show_daily_high
        high_line = line.new(
            x1=bar_index - 100, y1=prev_high, 
            x2=bar_index, y2=prev_high, 
            color=high_color, 
            width=line_width, 
            style=line_style_setting, 
            extend=extend_setting
        )
        array.push(daily_high_lines, high_line)
        
        // Add label for daily high
        if show_labels
            label_text = "PDH" + (show_prices ? " (" + str.tostring(prev_high, "#.####") + ")" : "")
            high_label = label.new(
                x=bar_index, y=prev_high, 
                text=label_text, 
                style=label.style_label_left, 
                color=color.new(color.white, 100), 
                textcolor=high_color, 
                size=label_size_setting
            )
            array.push(daily_high_labels, high_label)
    
    // Draw daily low line
    if show_daily_low
        low_line = line.new(
            x1=bar_index - 100, y1=prev_low, 
            x2=bar_index, y2=prev_low, 
            color=low_color, 
            width=line_width, 
            style=line_style_setting, 
            extend=extend_setting
        )
        array.push(daily_low_lines, low_line)
        
        // Add label for daily low
        if show_labels
            label_text = "PDL" + (show_prices ? " (" + str.tostring(prev_low, "#.####") + ")" : "")
            low_label = label.new(
                x=bar_index, y=prev_low, 
                text=label_text, 
                style=label.style_label_left, 
                color=color.new(color.white, 100), 
                textcolor=low_color, 
                size=label_size_setting
            )
            array.push(daily_low_labels, low_label)

// === ALERTS ===
// Alert when price touches daily high
if show_daily_high and not na(prev_high) and (high >= prev_high or low <= prev_high) and (high[1] < prev_high and low[1] > prev_high)
    alert("Price touched Previous Day High: " + str.tostring(prev_high), alert.freq_once_per_bar)

// Alert when price touches daily low
if show_daily_low and not na(prev_low) and (high >= prev_low or low <= prev_low) and (high[1] < prev_low and low[1] > prev_low)
    alert("Price touched Previous Day Low: " + str.tostring(prev_low), alert.freq_once_per_bar)
